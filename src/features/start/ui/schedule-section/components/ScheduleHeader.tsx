import React from "react";

import styled from "styled-components";

// eslint-disable-next-line import/no-restricted-paths
import { Toggle } from "@/features/calendar/ui/big-calendar/toolbar/Toggle";

import { DateNavigation } from "./DateNavigation";

import type { ScheduleHeaderProps } from "../types";

const HeaderContainer = styled.div`
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #243544;
`;

export const ScheduleHeader: React.FC<ScheduleHeaderProps> = ({
  currentView,
  currentDate,
  onViewChange,
  onNavigateDate,
}) => {
  return (
    <HeaderContainer>
      <Title>スケジュール</Title>
      <DateNavigation
        currentDate={currentDate}
        currentView={currentView}
        onNavigate={onNavigateDate}
      />
      <Toggle
        defaultValue={currentView}
        onChange={(value) => onViewChange(value as "day" | "month")}
        toggleData={[
          { text: "日", value: "day" },
          { text: "月", value: "month" },
        ]}
        width={120}
      />
    </HeaderContainer>
  );
};
